#!/usr/bin/env python3
"""
Test script to verify WebSocket connection status check fix.
This script tests the _is_connection_open method to ensure it works with websockets 11.x+
"""

import sys
import os

# Add the ShopeeAPI directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'ShopeeAPI'))

def test_websocket_connection_check():
    """Test the WebSocket connection status check method."""
    print("\n=== Testing WebSocket Connection Status Check ===")
    
    try:
        # Import only what we need for the connection check
        from ShopeeAPI.services.websocket import WebSocketService
        
        # Create a minimal WebSocket service instance for testing
        # We'll bypass the full initialization and just test the method
        ws_service = object.__new__(WebSocketService)
        ws_service.ws_connection = None
        
        # Test 1: No connection
        print("Test 1: No WebSocket connection")
        result = ws_service._is_connection_open()
        print(f"  Result: {result} (Expected: False)")
        assert result == False, "Should return False when no connection"
        print("  ✓ PASS")
        
        # Test 2: Mock connection object with 'closed' attribute (websockets 11.x+)
        print("\nTest 2: Mock connection with 'closed' attribute (websockets 11.x+)")
        
        class MockConnection:
            def __init__(self, closed=False):
                self.closed = closed
        
        ws_service.ws_connection = MockConnection(closed=False)
        result = ws_service._is_connection_open()
        print(f"  Result: {result} (Expected: True)")
        assert result == True, "Should return True when connection is not closed"
        print("  ✓ PASS")
        
        ws_service.ws_connection = MockConnection(closed=True)
        result = ws_service._is_connection_open()
        print(f"  Result: {result} (Expected: False)")
        assert result == False, "Should return False when connection is closed"
        print("  ✓ PASS")
        
        # Test 3: Mock connection object with 'open' attribute (websockets 10.x)
        print("\nTest 3: Mock connection with 'open' attribute (websockets 10.x)")
        
        class MockOldConnection:
            def __init__(self, open=True):
                self.open = open
        
        ws_service.ws_connection = MockOldConnection(open=True)
        result = ws_service._is_connection_open()
        print(f"  Result: {result} (Expected: True)")
        assert result == True, "Should return True when connection is open"
        print("  ✓ PASS")
        
        ws_service.ws_connection = MockOldConnection(open=False)
        result = ws_service._is_connection_open()
        print(f"  Result: {result} (Expected: False)")
        assert result == False, "Should return False when connection is not open"
        print("  ✓ PASS")
        
        # Test 4: Connection object with neither attribute
        print("\nTest 4: Mock connection with neither 'open' nor 'closed' attribute")
        
        class MockUnknownConnection:
            pass
        
        ws_service.ws_connection = MockUnknownConnection()
        result = ws_service._is_connection_open()
        print(f"  Result: {result} (Expected: False)")
        assert result == False, "Should return False when neither attribute exists"
        print("  ✓ PASS")
        
        # Test 5: Connection object that raises exception
        print("\nTest 5: Mock connection that raises exception when accessing attributes")
        
        class MockErrorConnection:
            @property
            def closed(self):
                raise Exception("Test exception")
            
            @property
            def open(self):
                raise Exception("Test exception")
        
        ws_service.ws_connection = MockErrorConnection()
        result = ws_service._is_connection_open()
        print(f"  Result: {result} (Expected: False)")
        assert result == False, "Should return False when exception occurs"
        print("  ✓ PASS")
        
        print("\n=== All Tests Passed! ===")
        print("The WebSocket connection status check fix is working correctly.")
        
    except Exception as e:
        print(f"✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("WebSocket Connection Status Check Test")
    print("=" * 50)
    
    try:
        from ShopeeAPI.services.websocket import WebSocketService
        print("✓ Successfully imported WebSocketService")
    except ImportError as e:
        print(f"✗ Import error: {e}")
        sys.exit(1)
    
    # Run the test
    success = test_websocket_connection_check()
    
    if success:
        print("\n🎉 All tests completed successfully!")
        print("The WebSocket service should now work correctly with websockets 11.x+")
        print("\nThe fix resolves the error:")
        print("  ERROR: 'ClientConnection' object has no attribute 'open'")
        print("  WARNING: Failed to send initialization messages: 'ClientConnection' object has no attribute 'open'")
    else:
        print("\n❌ Tests failed!")
        sys.exit(1) 